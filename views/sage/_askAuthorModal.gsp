<div class="modal fade sage-modal" id="sageAuthorModal" tabindex="-1" role="dialog" aria-labelledby="sageModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header sage-modal-header">
        <a href="javascript:hideUserLogin();" class="close-sidebar pull-right" data-dismiss="modal">
          <i class="material-icons">close</i>
        </a>
      </div>
      <div class="modal-body">
        <form class="sage-login-form" style="margin-top: 0;">
          <div class="form-group">
            <textarea class="form-control sage-input" rows="8" id="userFormQuery" placeholder="Write your query"></textarea>
          </div>
          <div class="form-group">
            <img id="userFormCaptchaImg" src="#"/>
            <label class="benefits-text" for="captcha">Type the letters above in the box below</label>
            <g:textField class="form-control sage-input" id="userFormCaptcha" name="captcha"/>
          </div>            
          <div class="form-group">
            <input type="button" class="form-control sage-input sage-login-btn waves-effect waves-ripple full-width-input" onclick="javascript:userFormSubmit();" value="Submit">
          </div>
          <div class="form-group">
            <input type="hidden" id="userFormName" value="<%=session["userdetails"]!=null?session["userdetails"].name:""%>">
            <input type="hidden" id="userFormEmail" value="<%=session["userdetails"]!=null?session["userdetails"].email:""%>">
            <input type="hidden" id="userFormSubject" value="<%=session["userdetails"]!=null && "student".equals(session["userdetails"].userType)?"(Student): Ask the author":"(Instructor): Ask the author"%>">
            <div id="userform-query-error" style="color: red; display: none;">Please enter query</div>
          </div>              
        </form>
      </div>
    </div>
  </div>
</div>